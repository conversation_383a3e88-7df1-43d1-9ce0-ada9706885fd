{"name": "chat", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test-ui": "vitest --ui"}, "dependencies": {"@codemirror/basic-setup": "^0.20.0", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/language": "^6.10.8", "@codemirror/search": "^6.5.10", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.4", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@testing-library/user-event": "^14.5.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cross-spawn": "^7.0.6", "jotai": "^2.11.0", "lucide-react": "^0.453.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.7", "rehype-highlight": "^7.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "sonner": "^1.5.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2"}, "devDependencies": {"@eslint/js": "^9.11.1", "@testing-library/jest-dom": "^6.6.2", "@testing-library/react": "^16.0.1", "@types/jest": "^29.5.13", "@types/node": "^22.7.5", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.11", "@vitejs/plugin-react": "^4.3.2", "@vitest/ui": "^2.1.3", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "jsdom": "^25.0.1", "postcss": "^8.4.47", "sass-embedded": "^1.85.1", "tailwindcss": "^3.4.14", "typescript": "^5.5.3", "typescript-eslint": "^8.7.0", "vite": "^5.4.14", "vitest": "^2.1.3"}}