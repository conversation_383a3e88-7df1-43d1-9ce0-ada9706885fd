@import"https://fonts.googleapis.com/css2?family=Ubuntu+Sans:ital,wght@0,100..800;1,100..800&display=swap";#root{height:100vh;margin:auto;font-family:Inter}body{pointer-events:all!important}.fixed-scroll{overflow:scroll;scrollbar-width:thin;scrollbar-color:#ebecf0 transparent}.fixed-scroll:hover{scrollbar-color:#cdcdcd transparent}.fixed-scroll::-webkit-scrollbar{width:10px}.fixed-scroll::-webkit-scrollbar-thumb{background-color:#00000080;border-radius:10px}.fixed-scroll::-webkit-scrollbar-track{background:transparent}.markdown *{font-size:revert;font-weight:revert;padding:revert;margin:revert;list-style-type:revert;color:revert;-webkit-text-decoration:revert;text-decoration:revert}img{-webkit-user-select:none;-moz-user-select:none;user-select:none}._editSession_1nfqv_1{position:relative}._editSession_1nfqv_1:before{content:"";position:absolute;top:50%;left:50%;border:1px solid black;pointer-events:none;box-sizing:border-box;height:calc(100% - 4px);width:calc(100% - 8px);transform:translate(-50%,-50%);border-radius:6px}pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}/*!
  Theme: GitHub
  Description: Light theme as seen on github.com
  Author: github.com
  Maintainer: @Hirse
  Updated: 2021-05-15

  Outdated base version: https://github.com/primer/github-syntax-light
  Current colors taken from GitHub's CSS
*/.hljs{color:#24292e;background:#fff}.hljs-doctag,.hljs-keyword,.hljs-meta .hljs-keyword,.hljs-template-tag,.hljs-template-variable,.hljs-type,.hljs-variable.language_{color:#d73a49}.hljs-title,.hljs-title.class_,.hljs-title.class_.inherited__,.hljs-title.function_{color:#6f42c1}.hljs-attr,.hljs-attribute,.hljs-literal,.hljs-meta,.hljs-number,.hljs-operator,.hljs-variable,.hljs-selector-attr,.hljs-selector-class,.hljs-selector-id{color:#005cc5}.hljs-regexp,.hljs-string,.hljs-meta .hljs-string{color:#032f62}.hljs-built_in,.hljs-symbol{color:#e36209}.hljs-comment,.hljs-code,.hljs-formula{color:#6a737d}.hljs-name,.hljs-quote,.hljs-selector-tag,.hljs-selector-pseudo{color:#22863a}.hljs-subst{color:#24292e}.hljs-section{color:#005cc5;font-weight:700}.hljs-bullet{color:#735c0f}.hljs-emphasis{color:#24292e;font-style:italic}.hljs-strong{color:#24292e;font-weight:700}.hljs-addition{color:#22863a;background-color:#f0fff4}.hljs-deletion{color:#b31d28;background-color:#ffeef0}._pendingVideo_1po3x_1{clip-path:inset(1px .9px .5px .8px round 50%)}._markdown_1po3x_5 code{white-space:break-spaces;max-width:100%;word-break:break-word;background:transparent!important;font-size:14px}._markdown_1po3x_5 p{word-break:break-word}._markdown_1po3x_5 ul{all:revert;margin:0;padding:0;list-style:inside}._markdown_1po3x_5 h2{font-weight:700}._markdown_1po3x_5 table{white-space:nowrap;display:block;overflow:scroll;scrollbar-width:auto;border-radius:2px}._markdown_1po3x_5 table th,._markdown_1po3x_5 table td{padding-inline:10px;text-align:start}._markdown_1po3x_5 table th{padding:10px}._markdown_1po3x_5 table tr:last-child td{padding-bottom:10px}._markdown_1po3x_5 table thead{border:1px solid lightgray;border-bottom:none;border-radius:3px 3px 0 0;padding:10px}._markdown_1po3x_5 table tbody{border:1px solid lightgray;border-top:none;border-radius:0 0 3px 3px;padding:10px}@font-face{font-family:Ubuntu Sans;font-style:normal;font-weight:100;src:url(/chat/fonts/ubuntu-sans/static/UbuntuSans-Thin.ttf) format("truetype")}@font-face{font-family:Ubuntu Sans;font-style:normal;font-weight:200;src:url(/chat/fonts/ubuntu-sans/static/UbuntuSans-ExtraLight.ttf) format("truetype")}@font-face{font-family:Ubuntu Sans;font-style:normal;font-weight:300;src:url(/chat/fonts/ubuntu-sans/static/UbuntuSans-Light.ttf) format("truetype")}@font-face{font-family:Ubuntu Sans;font-style:normal;font-weight:400;src:url(/chat/fonts/ubuntu-sans/static/UbuntuSans-Regular.ttf) format("truetype")}@font-face{font-family:Ubuntu Sans;font-style:normal;font-weight:500;src:url(/chat/fonts/ubuntu-sans/static/UbuntuSans-Medium.ttf) format("truetype")}@font-face{font-family:Ubuntu Sans;font-style:normal;font-weight:600;src:url(/chat/fonts/ubuntu-sans/static/UbuntuSans-SemiBold.ttf) format("truetype")}@font-face{font-family:Ubuntu Sans;font-style:normal;font-weight:700;src:url(/chat/fonts/ubuntu-sans/static/UbuntuSans-Bold.ttf) format("truetype")}@font-face{font-family:Ubuntu Sans;font-style:normal;font-weight:800;src:url(/chat/fonts/ubuntu-sans/static/UbuntuSans-ExtraBold.ttf) format("truetype")}@font-face{font-family:Ubuntu Mono;font-style:normal;font-weight:100;src:url(/chat/fonts/ubuntu-mono/static/UbuntuMono-Regular.ttf) format("truetype")}@font-face{font-family:Ubuntu Mono;font-style:normal;font-weight:200;src:url(/chat/fonts/ubuntu-mono/static/UbuntuMono-Regular.ttf) format("truetype")}@font-face{font-family:Ubuntu Mono;font-style:normal;font-weight:300;src:url(/chat/fonts/ubuntu-mono/static/UbuntuMono-Regular.ttf) format("truetype")}@font-face{font-family:Ubuntu Mono;font-style:normal;font-weight:400;src:url(/chat/fonts/ubuntu-mono/static/UbuntuMono-Regular.ttf) format("truetype")}@font-face{font-family:Ubuntu Mono;font-style:normal;font-weight:500;src:url(/chat/fonts/ubuntu-mono/static/UbuntuMono-Bold.ttf) format("truetype")}@font-face{font-family:Ubuntu Mono;font-style:normal;font-weight:600;src:url(/chat/fonts/ubuntu-mono/static/UbuntuMono-Bold.ttf) format("truetype")}@font-face{font-family:Ubuntu Mono;font-style:normal;font-weight:700;src:url(/chat/fonts/ubuntu-mono/static/UbuntuMono-Bold.ttf) format("truetype")}@font-face{font-family:Ubuntu Mono;font-style:normal;font-weight:800;src:url(/chat/fonts/ubuntu-mono/static/UbuntuMono-Bold.ttf) format("truetype")}@font-face{font-family:Ubuntu Mono;font-style:normal;font-weight:900;src:url(/chat/fonts/ubuntu-mono/static/UbuntuMono-Bold.ttf) format("truetype")}@font-face{font-family:Inter;font-style:normal;font-weight:100;src:url(/chat/fonts/Inter/static/Inter_28pt-Thin.ttf) format("truetype")}@font-face{font-family:Inter;font-style:normal;font-weight:200;src:url(/chat/fonts/Inter/static/Inter_28pt-Thin.ttf) format("truetype")}@font-face{font-family:Inter;font-style:normal;font-weight:300;src:url(/chat/fonts/Inter/static/Inter_28pt-Light.ttf) format("truetype")}@font-face{font-family:Inter;font-style:normal;font-weight:400;src:url(/chat/fonts/Inter/static/Inter_28pt-Regular.ttf) format("truetype")}@font-face{font-family:Inter;font-style:normal;font-weight:500;src:url(/chat/fonts/Inter/static/Inter_28pt-Medium.ttf) format("truetype")}@font-face{font-family:Inter;font-style:normal;font-weight:600;src:url(/chat/fonts/Inter/static/Inter_28pt-SemiBold.ttf) format("truetype")}@font-face{font-family:Inter;font-style:normal;font-weight:700;src:url(/chat/fonts/Inter/static/Inter_28pt-Bold.ttf) format("truetype")}@font-face{font-family:Inter;font-style:normal;font-weight:800;src:url(/chat/fonts/Inter/static/Inter_28pt-ExtraBold.ttf) format("truetype")}@font-face{font-family:Inter;font-style:normal;font-weight:900;src:url(/chat/fonts/Inter/static/Inter_28pt-ExtraBold.ttf) format("truetype")}*,:before,:after{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }*,:before,:after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}:before,:after{--tw-content: ""}html,:host{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}ol,ul,menu{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#9ca3af}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}button,[role=button]{cursor:pointer}:disabled{cursor:default}img,svg,video,canvas,audio,iframe,embed,object{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}:root{--main: #fbfbfb;--background: 0 0% 100%;--foreground: 0 0% 3.9%;--card: 0 0% 100%;--card-foreground: 0 0% 3.9%;--popover: 0 0% 100%;--popover-foreground: 0 0% 3.9%;--primary: 0 0% 9%;--primary-foreground: 0 0% 98%;--secondary: 0 0% 96.1%;--secondary-foreground: 0 0% 9%;--muted: 0 0% 96.1%;--muted-foreground: 0 0% 45.1%;--accent: 0 0% 96.1%;--accent-foreground: 0 0% 9%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--border: 0 0% 89.8%;--input: 0 0% 89.8%;--ring: 0 0% 3.9%;--chart-1: 12 76% 61%;--chart-2: 173 58% 39%;--chart-3: 197 37% 24%;--chart-4: 43 74% 66%;--chart-5: 27 87% 67%;--radius: .5rem}.dark{--main: rgb(1, 37, 21)}*{border-color:hsl(var(--border))}*{border-color:hsl(var(--border));outline-color:hsl(var(--ring) / .5)}body{background-color:hsl(var(--background));color:hsl(var(--foreground))}.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 801px){.container{max-width:801px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1080px){.container{max-width:1080px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.pointer-events-none{pointer-events:none}.\!pointer-events-auto{pointer-events:auto!important}.visible{visibility:visible}.invisible{visibility:hidden}.static{position:static}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.sticky{position:sticky}.inset-0{top:0;right:0;bottom:0;left:0}.inset-x-0{left:0;right:0}.inset-y-0{top:0;bottom:0}.-bottom-\[28px\]{bottom:-28px}.-bottom-\[3px\]{bottom:-3px}.-left-\[40px\]{left:-40px}.-top-\[1px\]{top:-1px}.bottom-0{bottom:0}.bottom-\[-1px\]{bottom:-1px}.left-0{left:0}.left-2{left:.5rem}.left-\[-1px\]{left:-1px}.left-\[0\.25em\]{left:.25em}.left-\[24px\]{left:24px}.left-\[34px\]{left:34px}.left-\[50\%\]{left:50%}.left-\[unset\]{left:unset}.right-0{right:0}.right-4{right:1rem}.right-\[-1px\]{right:-1px}.right-\[10px\]{right:10px}.right-\[1px\]{right:1px}.top-0{top:0}.top-4{top:1rem}.top-\[-1px\]{top:-1px}.top-\[10px\]{top:10px}.top-\[38px\]{top:38px}.top-\[50\%\]{top:50%}.top-\[8px\]{top:8px}.z-0{z-index:0}.z-10{z-index:10}.z-50{z-index:50}.z-\[11\]{z-index:11}.z-\[1\]{z-index:1}.z-\[999\]{z-index:999}.z-\[99\]{z-index:99}.m-auto{margin:auto}.-mx-1{margin-left:-.25rem;margin-right:-.25rem}.mx-0{margin-left:0;margin-right:0}.mx-\[8px\]{margin-left:8px;margin-right:8px}.mx-auto{margin-left:auto;margin-right:auto}.my-1{margin-top:.25rem;margin-bottom:.25rem}.\!ms-\[12px\]{margin-inline-start:12px!important}.\!mt-0{margin-top:0!important}.-mb-\[10px\]{margin-bottom:-10px}.-mb-\[7px\]{margin-bottom:-7px}.-ms-\[10px\]{margin-inline-start:-10px}.mb-1{margin-bottom:.25rem}.mb-\[10px\]{margin-bottom:10px}.mb-\[12px\]{margin-bottom:12px}.mb-\[14px\]{margin-bottom:14px}.mb-\[1px\]{margin-bottom:1px}.mb-\[26px\]{margin-bottom:26px}.mb-\[80px\]{margin-bottom:80px}.me-4{margin-inline-end:1rem}.me-\[10px\]{margin-inline-end:10px}.me-\[14px\]{margin-inline-end:14px}.me-\[18px\]{margin-inline-end:18px}.me-\[20px\]{margin-inline-end:20px}.me-\[2px\]{margin-inline-end:2px}.me-\[30px\]{margin-inline-end:30px}.me-\[3px\]{margin-inline-end:3px}.me-\[5px\]{margin-inline-end:5px}.me-\[6px\]{margin-inline-end:6px}.me-\[8px\]{margin-inline-end:8px}.ml-0{margin-left:0}.ml-\[23px\]{margin-left:23px}.ml-auto{margin-left:auto}.mr-0{margin-right:0}.ms-\[24px\]{margin-inline-start:24px}.ms-\[27px\]{margin-inline-start:27px}.ms-\[30px\]{margin-inline-start:30px}.ms-\[4px\]{margin-inline-start:4px}.ms-\[6px\]{margin-inline-start:6px}.mt-24{margin-top:6rem}.mt-\[0\]{margin-top:0}.mt-\[10px\]{margin-top:10px}.mt-\[30px\]{margin-top:30px}.mt-\[3px\]{margin-top:3px}.mt-\[40px\]{margin-top:40px}.mt-\[46px\]{margin-top:46px}.mt-\[4px\]{margin-top:4px}.mt-\[9px\]{margin-top:9px}.mt-auto{margin-top:auto}.block{display:block}.flex{display:flex}.inline-flex{display:inline-flex}.grid{display:grid}.hidden{display:none}.aspect-square{aspect-ratio:1 / 1}.\!size-\[17px\]{width:17px!important;height:17px!important}.size-\[14px\]{width:14px;height:14px}.size-\[15px\]{width:15px;height:15px}.size-\[16px\]{width:16px;height:16px}.size-\[18px\]{width:18px;height:18px}.size-\[25px\]{width:25px;height:25px}.size-\[26px\]{width:26px;height:26px}.size-\[28px\]{width:28px;height:28px}.size-\[30px\]{width:30px;height:30px}.size-\[330px\]{width:330px;height:330px}.size-\[36px\]{width:36px;height:36px}.size-\[38px\]{width:38px;height:38px}.size-\[44px\]{width:44px;height:44px}.h-0{height:0px}.h-10{height:2.5rem}.h-11{height:2.75rem}.h-2{height:.5rem}.h-2\.5{height:.625rem}.h-3\.5{height:.875rem}.h-4{height:1rem}.h-9{height:2.25rem}.h-\[-webkit-fill-available\]{height:-webkit-fill-available}.h-\[100\%\]{height:100%}.h-\[120px\]{height:120px}.h-\[14px\]{height:14px}.h-\[20px\]{height:20px}.h-\[21px\]{height:21px}.h-\[24px\]{height:24px}.h-\[28px\]{height:28px}.h-\[30px\]{height:30px}.h-\[32px\]{height:32px}.h-\[34px\]{height:34px}.h-\[35px\]{height:35px}.h-\[36px\]{height:36px}.h-\[38px\]{height:38px}.h-\[39px\]{height:39px}.h-\[46px\]{height:46px}.h-\[47px\]{height:47px}.h-\[48\.67px\]{height:48.67px}.h-\[48px\]{height:48px}.h-\[54px\]{height:54px}.h-\[58px\]{height:58px}.h-\[60px\]{height:60px}.h-\[68px\]{height:68px}.h-\[70px\]{height:70px}.h-\[74px\]{height:74px}.h-\[78px\]{height:78px}.h-\[80\%\]{height:80%}.h-\[80px\]{height:80px}.h-\[calc\(100\%-12px\)\]{height:calc(100% - 12px)}.h-\[calc\(100\%-4px\)\]{height:calc(100% - 4px)}.h-\[calc\(100\%-60px\)\]{height:calc(100% - 60px)}.h-\[calc\(100\%-68px\)\]{height:calc(100% - 68px)}.h-\[calc\(100vh-74px\)\]{height:calc(100vh - 74px)}.h-\[var\(--radix-select-trigger-height\)\]{height:var(--radix-select-trigger-height)}.h-auto{height:auto}.h-fit{height:-moz-fit-content;height:fit-content}.h-full{height:100%}.h-px{height:1px}.h-screen{height:100vh}.max-h-96{max-height:24rem}.max-h-\[200px\]{max-height:200px}.max-h-\[28px\]{max-height:28px}.max-h-\[300px\]{max-height:300px}.max-h-\[308px\]{max-height:308px}.max-h-\[30px\]{max-height:30px}.max-h-\[50\%\]{max-height:50%}.min-h-\[14px\]{min-height:14px}.min-h-\[28px\]{min-height:28px}.min-h-\[30px\]{min-height:30px}.min-h-\[40px\]{min-height:40px}.min-h-\[42px\]{min-height:42px}.min-h-\[48px\]{min-height:48px}.min-h-\[50px\]{min-height:50px}.min-h-\[58px\]{min-height:58px}.min-h-\[60px\]{min-height:60px}.min-h-\[70px\]{min-height:70px}.min-h-\[74px\]{min-height:74px}.min-h-\[78px\]{min-height:78px}.min-h-\[80px\]{min-height:80px}.min-h-\[unset\]{min-height:unset}.w-10{width:2.5rem}.w-2{width:.5rem}.w-2\.5{width:.625rem}.w-3{width:.75rem}.w-3\.5{width:.875rem}.w-3\/4{width:75%}.w-4{width:1rem}.w-\[1020px\]{width:1020px}.w-\[136px\]{width:136px}.w-\[14px\]{width:14px}.w-\[161px\]{width:161px}.w-\[168px\]{width:168px}.w-\[16px\]{width:16px}.w-\[246px\]{width:246px}.w-\[24px\]{width:24px}.w-\[28px\]{width:28px}.w-\[352px\]{width:352px}.w-\[36px\]{width:36px}.w-\[560px\]{width:560px}.w-\[600px\]{width:600px}.w-\[68px\]{width:68px}.w-\[73px\]{width:73px}.w-\[79px\]{width:79px}.w-\[84px\]{width:84px}.w-\[86px\]{width:86px}.w-\[95px\]{width:95px}.w-\[96px\]{width:96px}.w-\[calc\(100\%-412px\)\]{width:calc(100% - 412px)}.w-\[calc\(100\%-4px\)\]{width:calc(100% - 4px)}.w-\[calc\(100\%-min\(700px\,35vw\)\)\]{width:calc(100% - min(700px,35vw))}.w-\[calc\(100vw-352px-55px\)\]{width:calc(100vw - 407px)}.w-\[min\(700px\,35vw\)\]{width:min(700px,35vw)}.w-fit{width:-moz-fit-content;width:fit-content}.w-full{width:100%}.w-px{width:1px}.min-w-\[14px\]{min-width:14px}.min-w-\[16px\]{min-width:16px}.min-w-\[18px\]{min-width:18px}.min-w-\[200px\]{min-width:200px}.min-w-\[28px\]{min-width:28px}.min-w-\[352px\]{min-width:352px}.min-w-\[50\%\]{min-width:50%}.min-w-\[86px\]{min-width:86px}.min-w-\[8rem\]{min-width:8rem}.min-w-\[min\(1000px\,100\%\)\]{min-width:min(1000px,100%)}.min-w-\[unset\]{min-width:unset}.min-w-\[var\(--radix-select-trigger-width\)\]{min-width:var(--radix-select-trigger-width)}.min-w-max{min-width:-moz-max-content;min-width:max-content}.max-w-\[-webkit-fill-available\]{max-width:-webkit-fill-available}.max-w-\[1000px\]{max-width:1000px}.max-w-\[200px\]{max-width:200px}.max-w-\[210px\]{max-width:210px}.max-w-\[320px\]{max-width:320px}.max-w-\[378px\]{max-width:378px}.max-w-\[480px\]{max-width:480px}.max-w-\[608px\]{max-width:608px}.max-w-\[60px\]{max-width:60px}.max-w-\[80\%\]{max-width:80%}.max-w-\[80vw\]{max-width:80vw}.max-w-\[95\%\]{max-width:95%}.max-w-\[calc\(100vw-352px-55px\)\]{max-width:calc(100vw - 407px)}.max-w-\[min\(1000px\,100\%\)\]{max-width:min(1000px,100%)}.max-w-\[min\(1020px\,100\%\)\]{max-width:min(1020px,100%)}.max-w-\[min\(560px\,100\%\)\]{max-width:min(560px,100%)}.max-w-\[min\(560px\,90\%\)\]{max-width:min(560px,90%)}.max-w-\[min\(700px\,35vw\)\]{max-width:min(700px,35vw)}.max-w-\[unset\]{max-width:unset}.max-w-fit{max-width:-moz-fit-content;max-width:fit-content}.max-w-full{max-width:100%}.max-w-lg{max-width:32rem}.flex-1{flex:1 1 0%}.shrink-0{flex-shrink:0}.origin-bottom{transform-origin:bottom}.-translate-y-1\/2{--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-translate-y-\[-50\%\]{--tw-translate-y: 50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-translate-y-\[70px\]{--tw-translate-y: -70px;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.translate-x-0{--tw-translate-x: 0px;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.translate-x-\[-50\%\]{--tw-translate-x: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.translate-x-\[100\%\]{--tw-translate-x: 100%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.translate-y-\[-50\%\]{--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.translate-y-\[0px\]{--tw-translate-y: 0px;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.rotate-90{--tw-rotate: 90deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.animate-background-shift{animation:background-shift 5s linear infinite}@keyframes fade-in{0%{opacity:0}to{opacity:1}}.animate-fade-in{animation:fade-in .3s linear}.animate-none{animation:none}@keyframes pulse{50%{opacity:.5}}.animate-pulse{animation:pulse 2s cubic-bezier(.4,0,.6,1) infinite}.\!cursor-pointer{cursor:pointer!important}.cursor-auto{cursor:auto}.cursor-default{cursor:default}.cursor-pointer{cursor:pointer}.select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.resize-none{resize:none}.resize{resize:both}.snap-end{scroll-snap-align:end}.flex-row{flex-direction:row}.flex-row-reverse{flex-direction:row-reverse}.flex-col{flex-direction:column}.flex-col-reverse{flex-direction:column-reverse}.flex-wrap{flex-wrap:wrap}.items-start{align-items:flex-start}.items-end{align-items:flex-end}.items-center{align-items:center}.items-stretch{align-items:stretch}.\!justify-start{justify-content:flex-start!important}.justify-start{justify-content:flex-start}.justify-end{justify-content:flex-end}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-0{gap:0px}.gap-1\.5{gap:.375rem}.gap-2{gap:.5rem}.gap-4{gap:1rem}.gap-\[10px\]{gap:10px}.gap-\[12px\]{gap:12px}.gap-\[14px\]{gap:14px}.gap-\[16px\]{gap:16px}.gap-\[17px\]{gap:17px}.gap-\[20px\]{gap:20px}.gap-\[22px\]{gap:22px}.gap-\[27px\]{gap:27px}.gap-\[3px\]{gap:3px}.gap-\[4px\]{gap:4px}.gap-\[5px\]{gap:5px}.gap-\[6px\]{gap:6px}.gap-\[7px\]{gap:7px}.gap-\[8px\]{gap:8px}.space-x-2>:not([hidden])~:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.5rem * var(--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-1\.5>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(.375rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.375rem * var(--tw-space-y-reverse))}.space-y-2>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.5rem * var(--tw-space-y-reverse))}.self-start{align-self:flex-start}.self-end{align-self:flex-end}.self-center{align-self:center}.self-stretch{align-self:stretch}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-visible{overflow:visible}.overflow-x-auto{overflow-x:auto}.overflow-x-hidden{overflow-x:hidden}.overflow-y-visible{overflow-y:visible}.overflow-ellipsis,.text-ellipsis{text-overflow:ellipsis}.whitespace-nowrap{white-space:nowrap}.text-wrap{text-wrap:wrap}.text-nowrap{text-wrap:nowrap}.break-words{overflow-wrap:break-word}.rounded{border-radius:.25rem}.rounded-\[10px\]{border-radius:10px}.rounded-\[12px\]{border-radius:12px}.rounded-\[14px\]{border-radius:14px}.rounded-\[16px\]{border-radius:16px}.rounded-\[20px\]{border-radius:20px}.rounded-\[22px\]{border-radius:22px}.rounded-\[2px\]{border-radius:2px}.rounded-\[3px\]{border-radius:3px}.rounded-\[4px\]{border-radius:4px}.rounded-\[5px\]{border-radius:5px}.rounded-\[6\.5px\]{border-radius:6.5px}.rounded-\[6px\]{border-radius:6px}.rounded-\[7px\]{border-radius:7px}.rounded-\[8px\]{border-radius:8px}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:var(--radius)}.rounded-md{border-radius:calc(var(--radius) - 2px)}.rounded-none{border-radius:0}.rounded-sm{border-radius:calc(var(--radius) - 4px)}.rounded-s-\[16px\]{border-start-start-radius:16px;border-end-start-radius:16px}.rounded-br-none{border-bottom-right-radius:0}.rounded-ee-\[16px\]{border-end-end-radius:16px}.rounded-es-\[16px\]{border-end-start-radius:16px}.rounded-se-\[16px\]{border-start-end-radius:16px}.rounded-ss-\[16px\]{border-start-start-radius:16px}.\!border-0{border-width:0px!important}.border{border-width:1px}.border-2{border-width:2px}.border-\[0\.6px\]{border-width:.6px}.border-\[2px\]{border-width:2px}.border-\[6px\]{border-width:6px}.border-y-\[10px\]{border-top-width:10px;border-bottom-width:10px}.border-b{border-bottom-width:1px}.border-b-\[0\.6px\]{border-bottom-width:.6px}.border-b-\[12px\]{border-bottom-width:12px}.border-e{border-inline-end-width:1px}.border-l{border-left-width:1px}.border-r{border-right-width:1px}.border-t{border-top-width:1px}.border-t-0{border-top-width:0px}.border-solid{border-style:solid}.border-none{border-style:none}.\!border-black{--tw-border-opacity: 1 !important;border-color:rgb(0 0 0 / var(--tw-border-opacity, 1))!important}.\!border-transparent{border-color:transparent!important}.border-\[\#EBECF0\]{--tw-border-opacity: 1;border-color:rgb(235 236 240 / var(--tw-border-opacity, 1))}.border-\[\#EDEFF3\]{--tw-border-opacity: 1;border-color:rgb(237 239 243 / var(--tw-border-opacity, 1))}.border-\[\#EEEEEE\]{--tw-border-opacity: 1;border-color:rgb(238 238 238 / var(--tw-border-opacity, 1))}.border-\[\#F3F5F9\]{--tw-border-opacity: 1;border-color:rgb(243 245 249 / var(--tw-border-opacity, 1))}.border-\[\#F5F9F7\]{--tw-border-opacity: 1;border-color:rgb(245 249 247 / var(--tw-border-opacity, 1))}.border-\[\#F9FAFC\]{--tw-border-opacity: 1;border-color:rgb(249 250 252 / var(--tw-border-opacity, 1))}.border-\[\#dedcdc\]{--tw-border-opacity: 1;border-color:rgb(222 220 220 / var(--tw-border-opacity, 1))}.border-\[\#ebecf0\]{--tw-border-opacity: 1;border-color:rgb(235 236 240 / var(--tw-border-opacity, 1))}.border-\[\#eeeeee\]{--tw-border-opacity: 1;border-color:rgb(238 238 238 / var(--tw-border-opacity, 1))}.border-black{--tw-border-opacity: 1;border-color:rgb(0 0 0 / var(--tw-border-opacity, 1))}.border-input{border-color:hsl(var(--input))}.border-muted{--tw-border-opacity: 1;border-color:rgb(235 236 240 / var(--tw-border-opacity, 1))}.border-primary{border-color:hsl(var(--primary))}.border-transparent{border-color:transparent}.border-white{--tw-border-opacity: 1;border-color:rgb(255 255 255 / var(--tw-border-opacity, 1))}.border-b-\[\#EBECF0\],.border-b-\[\#ebecf0\]{--tw-border-opacity: 1;border-bottom-color:rgb(235 236 240 / var(--tw-border-opacity, 1))}.\!bg-\[\#F5F6F8\]{--tw-bg-opacity: 1 !important;background-color:rgb(245 246 248 / var(--tw-bg-opacity, 1))!important}.\!bg-\[\#FAFAFA\]{--tw-bg-opacity: 1 !important;background-color:rgb(250 250 250 / var(--tw-bg-opacity, 1))!important}.\!bg-\[\#FDF2F1\]{--tw-bg-opacity: 1 !important;background-color:rgb(253 242 241 / var(--tw-bg-opacity, 1))!important}.\!bg-\[\#f5f6f8\]{--tw-bg-opacity: 1 !important;background-color:rgb(245 246 248 / var(--tw-bg-opacity, 1))!important}.\!bg-black-main{--tw-bg-opacity: 1 !important;background-color:rgb(21 21 21 / var(--tw-bg-opacity, 1))!important}.\!bg-gray-300{--tw-bg-opacity: 1 !important;background-color:rgb(209 213 219 / var(--tw-bg-opacity, 1))!important}.\!bg-gray-4{--tw-bg-opacity: 1 !important;background-color:rgb(245 246 248 / var(--tw-bg-opacity, 1))!important}.\!bg-white{--tw-bg-opacity: 1 !important;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))!important}.bg-\[\#006E53\]{--tw-bg-opacity: 1;background-color:rgb(0 110 83 / var(--tw-bg-opacity, 1))}.bg-\[\#EBECF0\]{--tw-bg-opacity: 1;background-color:rgb(235 236 240 / var(--tw-bg-opacity, 1))}.bg-\[\#F2F0FC\]{--tw-bg-opacity: 1;background-color:rgb(242 240 252 / var(--tw-bg-opacity, 1))}.bg-\[\#F5F6F8\]{--tw-bg-opacity: 1;background-color:rgb(245 246 248 / var(--tw-bg-opacity, 1))}.bg-\[\#F5F9F7\]{--tw-bg-opacity: 1;background-color:rgb(245 249 247 / var(--tw-bg-opacity, 1))}.bg-\[\#FAFAFA\]{--tw-bg-opacity: 1;background-color:rgb(250 250 250 / var(--tw-bg-opacity, 1))}.bg-\[\#FBFBFB\]{--tw-bg-opacity: 1;background-color:rgb(251 251 251 / var(--tw-bg-opacity, 1))}.bg-\[\#ebecf0\]{--tw-bg-opacity: 1;background-color:rgb(235 236 240 / var(--tw-bg-opacity, 1))}.bg-\[\#f0eeee\]{--tw-bg-opacity: 1;background-color:rgb(240 238 238 / var(--tw-bg-opacity, 1))}.bg-\[\#f5f5f9\]{--tw-bg-opacity: 1;background-color:rgb(245 245 249 / var(--tw-bg-opacity, 1))}.bg-\[\#f5f6f8\]{--tw-bg-opacity: 1;background-color:rgb(245 246 248 / var(--tw-bg-opacity, 1))}.bg-background{background-color:hsl(var(--background))}.bg-black\/80{background-color:#000c}.bg-border{background-color:hsl(var(--border))}.bg-destructive{background-color:hsl(var(--destructive))}.bg-gray-200{--tw-bg-opacity: 1;background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))}.bg-green-light{--tw-bg-opacity: 1;background-color:rgb(245 249 247 / var(--tw-bg-opacity, 1))}.bg-green-main{--tw-bg-opacity: 1;background-color:rgb(0 110 83 / var(--tw-bg-opacity, 1))}.bg-main{background-color:var(--main)}.bg-muted{--tw-bg-opacity: 1;background-color:rgb(235 236 240 / var(--tw-bg-opacity, 1))}.bg-popover{background-color:hsl(var(--popover))}.bg-primary{background-color:hsl(var(--primary))}.bg-secondary{background-color:hsl(var(--secondary))}.bg-white{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.fill-current{fill:currentColor}.\!p-\[4px_2px\]{padding:4px 2px!important}.p-0{padding:0}.p-1{padding:.25rem}.p-4{padding:1rem}.p-6{padding:1.5rem}.p-\[0\.9rem\]{padding:.9rem}.p-\[10px\]{padding:10px}.p-\[13px_22px_17px_22px\]{padding:13px 22px 17px}.p-\[14px\]{padding:14px}.p-\[16px\]{padding:16px}.p-\[20px\]{padding:20px}.p-\[20px_22px_24px_22px\]{padding:20px 22px 24px}.p-\[5px\]{padding:5px}.p-\[5px_16px_7px_16px\]{padding:5px 16px 7px}.p-\[6px\]{padding:6px}.p-\[8px\]{padding:8px}.px-0{padding-left:0;padding-right:0}.px-2{padding-left:.5rem;padding-right:.5rem}.px-3{padding-left:.75rem;padding-right:.75rem}.px-4{padding-left:1rem;padding-right:1rem}.px-8{padding-left:2rem;padding-right:2rem}.px-\[10px\]{padding-left:10px;padding-right:10px}.px-\[12px\]{padding-left:12px;padding-right:12px}.px-\[14px\]{padding-left:14px;padding-right:14px}.px-\[20px\]{padding-left:20px;padding-right:20px}.px-\[24px\]{padding-left:24px;padding-right:24px}.px-\[29\.5px\]{padding-left:29.5px;padding-right:29.5px}.px-\[2px\]{padding-left:2px;padding-right:2px}.px-\[34px\]{padding-left:34px;padding-right:34px}.px-\[39px\]{padding-left:39px;padding-right:39px}.px-\[8px\]{padding-left:8px;padding-right:8px}.py-1{padding-top:.25rem;padding-bottom:.25rem}.py-1\.5{padding-top:.375rem;padding-bottom:.375rem}.py-2{padding-top:.5rem;padding-bottom:.5rem}.py-\[10px\]{padding-top:10px;padding-bottom:10px}.py-\[12px\]{padding-top:12px;padding-bottom:12px}.py-\[13px\]{padding-top:13px;padding-bottom:13px}.py-\[14px\]{padding-top:14px;padding-bottom:14px}.py-\[42px\]{padding-top:42px;padding-bottom:42px}.py-\[4px\]{padding-top:4px;padding-bottom:4px}.py-\[5px\]{padding-top:5px;padding-bottom:5px}.py-\[8px\]{padding-top:8px;padding-bottom:8px}.pb-0{padding-bottom:0}.pb-4{padding-bottom:1rem}.pb-\[11px\]{padding-bottom:11px}.pb-\[14px\]{padding-bottom:14px}.pb-\[2px\]{padding-bottom:2px}.pb-\[4px\]{padding-bottom:4px}.pb-\[5px\]{padding-bottom:5px}.pb-\[8px\]{padding-bottom:8px}.pe-0{padding-inline-end:0px}.pe-\[108px\]{padding-inline-end:108px}.pe-\[10px\]{padding-inline-end:10px}.pe-\[12px\]{padding-inline-end:12px}.pe-\[14px\]{padding-inline-end:14px}.pe-\[18px\]{padding-inline-end:18px}.pe-\[20px\]{padding-inline-end:20px}.pe-\[38px\]{padding-inline-end:38px}.pe-\[6px\]{padding-inline-end:6px}.pe-\[8px\]{padding-inline-end:8px}.pl-8{padding-left:2rem}.pr-2{padding-right:.5rem}.ps-\[10px\]{padding-inline-start:10px}.ps-\[12px\]{padding-inline-start:12px}.ps-\[14px\]{padding-inline-start:14px}.ps-\[15px\]{padding-inline-start:15px}.ps-\[16px\]{padding-inline-start:16px}.ps-\[22px\]{padding-inline-start:22px}.ps-\[30px\]{padding-inline-start:30px}.ps-\[35px\]{padding-inline-start:35px}.ps-\[4px\]{padding-inline-start:4px}.ps-\[5px\]{padding-inline-start:5px}.ps-\[6px\]{padding-inline-start:6px}.ps-\[8px\]{padding-inline-start:8px}.pt-0{padding-top:0}.pt-\[10px\]{padding-top:10px}.pt-\[11px\]{padding-top:11px}.pt-\[1px\]{padding-top:1px}.pt-\[4px\]{padding-top:4px}.pt-\[6px\]{padding-top:6px}.text-center{text-align:center}.font-ibm-plex-mono{font-family:IBM Plex Mono}.font-inter{font-family:inter}.font-ubuntu-mono{font-family:Ubuntu Mono}.text-\[11px\]{font-size:11px}.text-\[12px\]{font-size:12px}.text-\[13px\]{font-size:13px}.text-\[14px\]{font-size:14px}.text-\[15px\]{font-size:15px}.text-\[16px\]{font-size:16px}.text-\[18px\]{font-size:18px}.text-\[20px\]{font-size:20px}.text-\[8px\]{font-size:8px}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:.875rem;line-height:1.25rem}.text-xs{font-size:.75rem;line-height:1rem}.font-bold{font-weight:700}.font-light{font-weight:300}.font-medium{font-weight:500}.font-normal{font-weight:400}.font-semibold{font-weight:600}.capitalize{text-transform:capitalize}.leading-\[14px\]{line-height:14px}.leading-\[16px\]{line-height:16px}.leading-\[18px\]{line-height:18px}.leading-\[19px\]{line-height:19px}.leading-\[26px\]{line-height:26px}.leading-none{line-height:1}.tracking-tight{letter-spacing:-.025em}.tracking-widest{letter-spacing:.1em}.\!text-\[\#282828\]{--tw-text-opacity: 1 !important;color:rgb(40 40 40 / var(--tw-text-opacity, 1))!important}.\!text-white{--tw-text-opacity: 1 !important;color:rgb(255 255 255 / var(--tw-text-opacity, 1))!important}.text-\[\#151515\]{--tw-text-opacity: 1;color:rgb(21 21 21 / var(--tw-text-opacity, 1))}.text-\[\#282828\]{--tw-text-opacity: 1;color:rgb(40 40 40 / var(--tw-text-opacity, 1))}.text-\[\#333\]{--tw-text-opacity: 1;color:rgb(51 51 51 / var(--tw-text-opacity, 1))}.text-\[\#3C8C71\]{--tw-text-opacity: 1;color:rgb(60 140 113 / var(--tw-text-opacity, 1))}.text-\[\#656565\]{--tw-text-opacity: 1;color:rgb(101 101 101 / var(--tw-text-opacity, 1))}.text-\[\#A9A9A9\]{--tw-text-opacity: 1;color:rgb(169 169 169 / var(--tw-text-opacity, 1))}.text-\[\#A9AFB7\]{--tw-text-opacity: 1;color:rgb(169 175 183 / var(--tw-text-opacity, 1))}.text-\[\#AEB4BB\]{--tw-text-opacity: 1;color:rgb(174 180 187 / var(--tw-text-opacity, 1))}.text-\[\#CDCDCD\]{--tw-text-opacity: 1;color:rgb(205 205 205 / var(--tw-text-opacity, 1))}.text-\[\#ef5350\]{--tw-text-opacity: 1;color:rgb(239 83 80 / var(--tw-text-opacity, 1))}.text-black{--tw-text-opacity: 1;color:rgb(0 0 0 / var(--tw-text-opacity, 1))}.text-current{color:currentColor}.text-destructive-foreground{color:hsl(var(--destructive-foreground))}.text-foreground{color:hsl(var(--foreground))}.text-gray-900{--tw-text-opacity: 1;color:rgb(17 24 39 / var(--tw-text-opacity, 1))}.text-popover-foreground{color:hsl(var(--popover-foreground))}.text-primary{color:hsl(var(--primary))}.text-primary-foreground{color:hsl(var(--primary-foreground))}.text-secondary-foreground{color:hsl(var(--secondary-foreground))}.text-white{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.underline{text-decoration-line:underline}.underline-offset-4{text-underline-offset:4px}.opacity-50{opacity:.5}.opacity-60{opacity:.6}.opacity-70{opacity:.7}.opacity-\[0\.3\]{opacity:.3}.opacity-\[33\%\]{opacity:33%}.\!shadow-main{--tw-shadow: 0 3px 3px 0 #00000005 !important;--tw-shadow-colored: 0 3px 3px 0 var(--tw-shadow-color) !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.\!shadow-none{--tw-shadow: 0 0 #0000 !important;--tw-shadow-colored: 0 0 #0000 !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.shadow-lg{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-main{--tw-shadow: 0 3px 3px 0 #00000005;--tw-shadow-colored: 0 3px 3px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-main-inset{--tw-shadow: 0px 0px 3px 0px #00000054 inset;--tw-shadow-colored: inset 0px 0px 3px 0px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-md{--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.\!shadow-main{--tw-shadow-color: var(--main) !important;--tw-shadow: var(--tw-shadow-colored) !important}.shadow-main{--tw-shadow-color: var(--main);--tw-shadow: var(--tw-shadow-colored) }.outline-none{outline:2px solid transparent;outline-offset:2px}.outline{outline-style:solid}.\!ring-0{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)!important}.\!ring-offset-0{--tw-ring-offset-width: 0px !important}.ring-offset-background{--tw-ring-offset-color: hsl(var(--background)) }.blur{--tw-blur: blur(8px);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.blur-\[4px\]{--tw-blur: blur(4px);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-colors{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-none{transition-property:none}.transition-opacity{transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.duration-200{transition-duration:.2s}.duration-500{transition-duration:.5s}.ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@keyframes enter{0%{opacity:var(--tw-enter-opacity, 1);transform:translate3d(var(--tw-enter-translate-x, 0),var(--tw-enter-translate-y, 0),0) scale3d(var(--tw-enter-scale, 1),var(--tw-enter-scale, 1),var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity, 1);transform:translate3d(var(--tw-exit-translate-x, 0),var(--tw-exit-translate-y, 0),0) scale3d(var(--tw-exit-scale, 1),var(--tw-exit-scale, 1),var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0))}}.animate-in{animation-name:enter;animation-duration:.15s;--tw-enter-opacity: initial;--tw-enter-scale: initial;--tw-enter-rotate: initial;--tw-enter-translate-x: initial;--tw-enter-translate-y: initial }.fade-in-0{--tw-enter-opacity: 0 }.zoom-in-95{--tw-enter-scale: .95 }.duration-200{animation-duration:.2s}.duration-500{animation-duration:.5s}.ease-in-out{animation-timing-function:cubic-bezier(.4,0,.2,1)}.running{animation-play-state:running}.no-scrollbar::-webkit-scrollbar{display:none}.no-scrollbar{-ms-overflow-style:none;scrollbar-width:none}.box-shadow-none{box-shadow:none!important}.\[box-shadow\:0_-0\.6px_0px_0px_\#F3F5F9\]{box-shadow:0 -.6px #f3f5f9}.\[box-shadow\:0px_0px_25px_0px_\#0000000A\]{box-shadow:0 0 25px #0000000a}.\[box-shadow\:0px_0px_30px_0px_\#0000001F\]{box-shadow:0 0 30px #0000001f}.\[box-shadow\:0px_2px_4px_0px_\#00403029\,0px_1px_5\.5px_0px_\#006E5329\]{box-shadow:0 2px 4px #00403029,0 1px 5.5px #006e5329}.\[box-shadow\:_0px_8px_20px_-8px_\#00000012\]{box-shadow:0 8px 20px -8px #00000012}.\[direction\:ltr\]{direction:ltr}.\[direction\:rtl\]{direction:rtl}.\[justify-self\:end\]{justify-self:end}.\[scroll-snap-type\:y_mandatory\]{scroll-snap-type:y mandatory}.\[stroke-width\:2px\]{stroke-width:2px}.\[transition-duration\:600ms\]{transition-duration:.6s}.\[word-break\:break-all\]{word-break:break-all}.\[word-break\:break-word\]{word-break:break-word}.file\:border-0::file-selector-button{border-width:0px}.file\:bg-transparent::file-selector-button{background-color:transparent}.file\:text-sm::file-selector-button{font-size:.875rem;line-height:1.25rem}.file\:font-medium::file-selector-button{font-weight:500}.file\:text-foreground::file-selector-button{color:hsl(var(--foreground))}.placeholder\:font-light::-moz-placeholder{font-weight:300}.placeholder\:font-light::placeholder{font-weight:300}.placeholder\:text-\[\#282828\]::-moz-placeholder{--tw-text-opacity: 1;color:rgb(40 40 40 / var(--tw-text-opacity, 1))}.placeholder\:text-\[\#282828\]::placeholder{--tw-text-opacity: 1;color:rgb(40 40 40 / var(--tw-text-opacity, 1))}.after\:absolute:after{content:var(--tw-content);position:absolute}.after\:inset-y-0:after{content:var(--tw-content);top:0;bottom:0}.after\:left-1\/2:after{content:var(--tw-content);left:50%}.after\:w-1:after{content:var(--tw-content);width:.25rem}.after\:-translate-x-1\/2:after{content:var(--tw-content);--tw-translate-x: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.focus-within\:\!bg-white:focus-within{--tw-bg-opacity: 1 !important;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))!important}.hover\:visible:hover{visibility:visible}@keyframes background-shift{0%,to{background-position-x:20%}50%{background-position-x:80%}}.hover\:animate-background-shift:hover{animation:background-shift 5s linear infinite}.hover\:cursor-text:hover{cursor:text}.hover\:rounded-\[6px\]:hover{border-radius:6px}.hover\:border-\[\#E4E6EA\]:hover{--tw-border-opacity: 1;border-color:rgb(228 230 234 / var(--tw-border-opacity, 1))}.hover\:border-\[\#E9EBEF\]:hover{--tw-border-opacity: 1;border-color:rgb(233 235 239 / var(--tw-border-opacity, 1))}.hover\:\!bg-\[\#F5EFEF\]:hover{--tw-bg-opacity: 1 !important;background-color:rgb(245 239 239 / var(--tw-bg-opacity, 1))!important}.hover\:\!bg-\[\#FAF9FF\]:hover{--tw-bg-opacity: 1 !important;background-color:rgb(250 249 255 / var(--tw-bg-opacity, 1))!important}.hover\:\!bg-\[\#f5f6f8\]:hover{--tw-bg-opacity: 1 !important;background-color:rgb(245 246 248 / var(--tw-bg-opacity, 1))!important}.hover\:\!bg-white:hover{--tw-bg-opacity: 1 !important;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))!important}.hover\:bg-\[\#005C3F\]:hover{--tw-bg-opacity: 1;background-color:rgb(0 92 63 / var(--tw-bg-opacity, 1))}.hover\:bg-\[\#EBE9F5\]:hover{--tw-bg-opacity: 1;background-color:rgb(235 233 245 / var(--tw-bg-opacity, 1))}.hover\:bg-\[\#EBECF0\]:hover{--tw-bg-opacity: 1;background-color:rgb(235 236 240 / var(--tw-bg-opacity, 1))}.hover\:bg-\[\#F3F5F9\]:hover{--tw-bg-opacity: 1;background-color:rgb(243 245 249 / var(--tw-bg-opacity, 1))}.hover\:bg-\[\#F5F6F8\]:hover{--tw-bg-opacity: 1;background-color:rgb(245 246 248 / var(--tw-bg-opacity, 1))}.hover\:bg-\[\#F5F9F3\]:hover{--tw-bg-opacity: 1;background-color:rgb(245 249 243 / var(--tw-bg-opacity, 1))}.hover\:bg-\[\#FAFAFA\]:hover{--tw-bg-opacity: 1;background-color:rgb(250 250 250 / var(--tw-bg-opacity, 1))}.hover\:bg-\[\#FBFBFB\]:hover{--tw-bg-opacity: 1;background-color:rgb(251 251 251 / var(--tw-bg-opacity, 1))}.hover\:bg-\[\#f3f5f9\]:hover{--tw-bg-opacity: 1;background-color:rgb(243 245 249 / var(--tw-bg-opacity, 1))}.hover\:bg-accent:hover{background-color:hsl(var(--accent))}.hover\:bg-destructive\/90:hover{background-color:hsl(var(--destructive) / .9)}.hover\:bg-green-hover:hover{--tw-bg-opacity: 1;background-color:rgb(0 92 63 / var(--tw-bg-opacity, 1))}.hover\:bg-main:hover{background-color:var(--main)}.hover\:bg-primary\/90:hover{background-color:hsl(var(--primary) / .9)}.hover\:bg-secondary\/80:hover{background-color:hsl(var(--secondary) / .8)}.hover\:bg-white:hover{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.hover\:text-\[\#151515\]:hover{--tw-text-opacity: 1;color:rgb(21 21 21 / var(--tw-text-opacity, 1))}.hover\:text-\[\#282828\]:hover{--tw-text-opacity: 1;color:rgb(40 40 40 / var(--tw-text-opacity, 1))}.hover\:text-accent-foreground:hover{color:hsl(var(--accent-foreground))}.hover\:underline:hover{text-decoration-line:underline}.hover\:opacity-100:hover{opacity:1}.focus\:\!bg-white:focus{--tw-bg-opacity: 1 !important;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))!important}.focus\:bg-accent:focus{background-color:hsl(var(--accent))}.focus\:text-accent-foreground:focus{color:hsl(var(--accent-foreground))}.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}.focus\:ring-2:focus{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.focus\:ring-ring:focus{--tw-ring-color: hsl(var(--ring)) }.focus\:ring-offset-2:focus{--tw-ring-offset-width: 2px }.focus-visible\:outline-none:focus-visible{outline:2px solid transparent;outline-offset:2px}.focus-visible\:ring-1:focus-visible{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.focus-visible\:ring-2:focus-visible{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.focus-visible\:ring-ring:focus-visible{--tw-ring-color: hsl(var(--ring)) }.focus-visible\:ring-offset-1:focus-visible{--tw-ring-offset-width: 1px }.focus-visible\:ring-offset-2:focus-visible{--tw-ring-offset-width: 2px }.disabled\:pointer-events-none:disabled{pointer-events:none}.disabled\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:opacity-50:disabled{opacity:.5}.group\/main:hover .group-hover\/main\:visible,.group:hover .group-hover\:visible{visibility:visible}.group:hover .group-hover\:block{display:block}.group:hover .group-hover\:flex{display:flex}.group:hover .group-hover\:hidden{display:none}.group:hover .group-hover\:border-\[\#FAFAFA\]{--tw-border-opacity: 1;border-color:rgb(250 250 250 / var(--tw-border-opacity, 1))}.group:hover .group-hover\:bg-\[\#EBECF0\]{--tw-bg-opacity: 1;background-color:rgb(235 236 240 / var(--tw-bg-opacity, 1))}.group:hover .group-hover\:text-\[\#656565\]{--tw-text-opacity: 1;color:rgb(101 101 101 / var(--tw-text-opacity, 1))}.group:hover .group-hover\:underline{text-decoration-line:underline}.group.toaster .group-\[\.toaster\]\:border-border{border-color:hsl(var(--border))}.group.toast .group-\[\.toast\]\:bg-muted{--tw-bg-opacity: 1;background-color:rgb(235 236 240 / var(--tw-bg-opacity, 1))}.group.toast .group-\[\.toast\]\:bg-primary{background-color:hsl(var(--primary))}.group.toaster .group-\[\.toaster\]\:bg-background{background-color:hsl(var(--background))}.group.toast .group-\[\.toast\]\:text-primary-foreground{color:hsl(var(--primary-foreground))}.group.toaster .group-\[\.toaster\]\:text-foreground{color:hsl(var(--foreground))}.group.toaster .group-\[\.toaster\]\:shadow-lg{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.peer:hover~.peer-hover\:visible{visibility:visible}.data-\[disabled\]\:pointer-events-none[data-disabled]{pointer-events:none}.data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction=vertical]{height:1px}.data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction=vertical]{width:100%}.data-\[side\=bottom\]\:translate-y-1[data-side=bottom]{--tw-translate-y: .25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\[side\=left\]\:-translate-x-1[data-side=left]{--tw-translate-x: -.25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\[side\=right\]\:translate-x-1[data-side=right]{--tw-translate-x: .25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\[side\=top\]\:-translate-y-1[data-side=top]{--tw-translate-y: -.25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction=vertical]{flex-direction:column}.data-\[state\=checked\]\:bg-primary[data-state=checked]{background-color:hsl(var(--primary))}.data-\[state\=open\]\:bg-accent[data-state=open]{background-color:hsl(var(--accent))}.data-\[state\=open\]\:bg-secondary[data-state=open]{background-color:hsl(var(--secondary))}.data-\[state\=checked\]\:text-primary-foreground[data-state=checked]{color:hsl(var(--primary-foreground))}.data-\[disabled\]\:opacity-50[data-disabled]{opacity:.5}.data-\[state\=closed\]\:duration-300[data-state=closed]{transition-duration:.3s}.data-\[state\=open\]\:duration-500[data-state=open]{transition-duration:.5s}.data-\[state\=open\]\:animate-in[data-state=open]{animation-name:enter;animation-duration:.15s;--tw-enter-opacity: initial;--tw-enter-scale: initial;--tw-enter-rotate: initial;--tw-enter-translate-x: initial;--tw-enter-translate-y: initial }.data-\[state\=closed\]\:animate-out[data-state=closed]{animation-name:exit;animation-duration:.15s;--tw-exit-opacity: initial;--tw-exit-scale: initial;--tw-exit-rotate: initial;--tw-exit-translate-x: initial;--tw-exit-translate-y: initial }.data-\[state\=closed\]\:fade-out-0[data-state=closed]{--tw-exit-opacity: 0 }.data-\[state\=open\]\:fade-in-0[data-state=open]{--tw-enter-opacity: 0 }.data-\[state\=closed\]\:zoom-out-95[data-state=closed]{--tw-exit-scale: .95 }.data-\[state\=open\]\:zoom-in-95[data-state=open]{--tw-enter-scale: .95 }.data-\[side\=bottom\]\:slide-in-from-top-2[data-side=bottom]{--tw-enter-translate-y: -.5rem }.data-\[side\=left\]\:slide-in-from-right-2[data-side=left]{--tw-enter-translate-x: .5rem }.data-\[side\=right\]\:slide-in-from-left-2[data-side=right]{--tw-enter-translate-x: -.5rem }.data-\[side\=top\]\:slide-in-from-bottom-2[data-side=top]{--tw-enter-translate-y: .5rem }.data-\[state\=closed\]\:slide-out-to-bottom[data-state=closed]{--tw-exit-translate-y: 100% }.data-\[state\=closed\]\:slide-out-to-left[data-state=closed]{--tw-exit-translate-x: -100% }.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state=closed]{--tw-exit-translate-x: -50% }.data-\[state\=closed\]\:slide-out-to-right[data-state=closed]{--tw-exit-translate-x: 100% }.data-\[state\=closed\]\:slide-out-to-top[data-state=closed]{--tw-exit-translate-y: -100% }.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state=closed]{--tw-exit-translate-y: -48% }.data-\[state\=open\]\:slide-in-from-bottom[data-state=open]{--tw-enter-translate-y: 100% }.data-\[state\=open\]\:slide-in-from-left[data-state=open]{--tw-enter-translate-x: -100% }.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state=open]{--tw-enter-translate-x: -50% }.data-\[state\=open\]\:slide-in-from-right[data-state=open]{--tw-enter-translate-x: 100% }.data-\[state\=open\]\:slide-in-from-top[data-state=open]{--tw-enter-translate-y: -100% }.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state=open]{--tw-enter-translate-y: -48% }.data-\[state\=closed\]\:duration-300[data-state=closed]{animation-duration:.3s}.data-\[state\=open\]\:duration-500[data-state=open]{animation-duration:.5s}.data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction=vertical]:after{content:var(--tw-content);left:0}.data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction=vertical]:after{content:var(--tw-content);height:.25rem}.data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction=vertical]:after{content:var(--tw-content);width:100%}.data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction=vertical]:after{content:var(--tw-content);--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction=vertical]:after{content:var(--tw-content);--tw-translate-x: 0px;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dark\:bg-gray-800:is(.dark *){--tw-bg-opacity: 1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1))}.dark\:bg-gray-900:is(.dark *){--tw-bg-opacity: 1;background-color:rgb(17 24 39 / var(--tw-bg-opacity, 1))}.dark\:text-gray-100:is(.dark *){--tw-text-opacity: 1;color:rgb(243 244 246 / var(--tw-text-opacity, 1))}@media (max-width: 2100px){.max-\[2100px\]\:w-\[calc\(100\%-200px\)\]{width:calc(100% - 200px)}}@media (max-width: 1700px){.max-\[1700px\]\:w-\[calc\(100\%-40px\)\]{width:calc(100% - 40px)}}@media (max-width: 1440px){.max-\[1440px\]\:w-\[calc\(100\%-160px\)\]{width:calc(100% - 160px)}}@media (max-width: 900px){.max-\[900px\]\:w-\[calc\(100\%-40px\)\]{width:calc(100% - 40px)}}@media not all and (min-width: 801px){.max-mobile\:block{display:block}.max-mobile\:hidden{display:none}.max-mobile\:w-full{width:100%}.max-mobile\:justify-between{justify-content:space-between}}@media (max-width: 800px){.max-\[800px\]\:w-full{width:100%}.max-\[800px\]\:max-w-full{max-width:100%}}@media (min-width: 640px){.sm\:max-w-sm{max-width:24rem}.sm\:flex-row{flex-direction:row}.sm\:justify-end{justify-content:flex-end}.sm\:space-x-2>:not([hidden])~:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.5rem * var(--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}.sm\:rounded-lg{border-radius:var(--radius)}.sm\:text-left{text-align:left}}@media (min-width: 801px){.min-\[801px\]\:hidden{display:none}}.\[\&\:first-child\]\:rounded-t-\[3px\]:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.\[\&\:first-child\]\:border-\[0px\]:first-child{border-width:0px}.\[\&\:last-child\]\:border-b:last-child{border-bottom-width:1px}.\[\&\>button\]\:hidden>button{display:none}.\[\&\>span\]\:line-clamp-1>span{overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1}.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction=vertical]>div{--tw-rotate: 90deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.\[\&_\.cm-gutters\]\:border-0 .cm-gutters{border-width:0px}.\[\&_\.cm-gutters\]\:bg-white .cm-gutters{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.\[\&_\.cm-gutters\]\:pe-\[1\.2em\] .cm-gutters{padding-inline-end:1.2em}.\[\&_\.cm-gutters\]\:ps-\[0\.5em\] .cm-gutters{padding-inline-start:.5em}.\[\&_\.cm-gutters\]\:text-\[\#bbb\] .cm-gutters{--tw-text-opacity: 1;color:rgb(187 187 187 / var(--tw-text-opacity, 1))}.\[\&_\.cm-panels\]\:sticky .cm-panels{position:sticky}.\[\&_\.cm-panels\]\:\!top-\[38px\] .cm-panels{top:38px!important}.\[\&_\.cm-panels\]\:h-\[39px\] .cm-panels{height:39px}.\[\&_\.cm-panels\]\:-translate-y-\[100\%\] .cm-panels{--tw-translate-y: -100%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.\[\&_\.cm-panels\]\:border-none .cm-panels{border-style:none}.\[\&_\.cm-panels\]\:bg-white .cm-panels{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.\[\&_\.cm-panels\]\:pl-\[20px\] .cm-panels{padding-left:20px}.\[\&_\.cm-scroller\>div\:nth-child\(2\)\]\:flex-1 .cm-scroller>div:nth-child(2){flex:1 1 0%}.\[\&_\.cm-scroller\>div\:nth-child\(2\)\]\:\[white-space\:break-spaces\] .cm-scroller>div:nth-child(2){white-space:break-spaces}.\[\&_\.cm-search\]\:bg-white .cm-search{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.\[\&_\.cm-search_\*\]\:hidden .cm-search *{display:none}.\[\&_\.cm-search_input\:first-child\]\:block .cm-search input:first-child{display:block}.\[\&_\.cm-search_input\:first-child\]\:w-\[300px\] .cm-search input:first-child{width:300px}.\[\&_\.cm-search_input\:first-child\]\:rounded-\[5px\] .cm-search input:first-child{border-radius:5px}.\[\&_svg\]\:pointer-events-none svg{pointer-events:none}.\[\&_svg\]\:size-4 svg{width:1rem;height:1rem}.\[\&_svg\]\:shrink-0 svg{flex-shrink:0}.\[\&_svg\]\:\[stroke\:\#006E53\] svg{stroke:#006e53}
